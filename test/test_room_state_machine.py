"""
房间状态机测试集
测试房间状态转换、生命周期管理、边界条件等
"""

import pytest
from datetime import datetime, timedelta
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone

from core.models import Room, RoomParticipant, RoomState, UserState
from core.services.room_manager import RoomManager
from events.models import EventTemplate, EventStep

User = get_user_model()


class RoomStateMachineTestCase(TestCase):
    """房间状态机测试基类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建测试用户
        self.user1 = User.objects.create_user(
            username='host_user',
            password='testpass123'
        )
        
        self.user2 = User.objects.create_user(
            username='participant_user',
            password='testpass123'
        )
        
        self.user3 = User.objects.create_user(
            username='another_user',
            password='testpass123'
        )
        
        # 创建测试模板
        self.template = EventTemplate.objects.create(
            name='状态机测试模板',
            description='用于状态机测试',
            creator=self.user1
        )
        
        EventStep.objects.create(
            template=self.template,
            name='测试步骤',
            step_type='FREE_CHAT',
            order=1,
            duration=300
        )
        
        # 初始化房间管理器
        self.room_manager = RoomManager()


class TestRoomStateDefinitions(RoomStateMachineTestCase):
    """测试房间状态定义"""
    
    def test_room_state_values(self):
        """测试房间状态值"""
        # 验证所有状态值
        expected_states = {
            'SCHEDULED': '已预约',
            'OPEN': '已开启',
            'WAITING_FOR_HOST': '等待房主',
            'READY': '准备就绪',
            'IN_PROGRESS': '活动中',
            'ENDED': '已结束',
            'CLOSED': '已关闭'
        }
        
        for state_value, state_label in expected_states.items():
            self.assertTrue(hasattr(RoomState, state_value))
            state = getattr(RoomState, state_value)
            self.assertEqual(state.value, state_value)
            self.assertEqual(state.label, state_label)
    
    def test_room_state_choices(self):
        """测试房间状态选择"""
        choices = RoomState.choices
        self.assertIsInstance(choices, list)
        self.assertEqual(len(choices), 7)  # 7个状态
        
        # 验证选择格式
        for choice in choices:
            self.assertIsInstance(choice, tuple)
            self.assertEqual(len(choice), 2)
            self.assertIsInstance(choice[0], str)  # 状态值
            self.assertIsInstance(choice[1], str)  # 状态标签


class TestStateTransitionRules(RoomStateMachineTestCase):
    """测试状态转换规则"""
    
    def test_valid_transitions(self):
        """测试有效的状态转换"""
        # 测试从SCHEDULED可以转换到的状态
        valid_from_scheduled = [RoomState.OPEN, RoomState.CLOSED]
        for target_state in valid_from_scheduled:
            self.assertIn(target_state, self.room_manager.VALID_STATE_TRANSITIONS[RoomState.SCHEDULED])
        
        # 测试从OPEN可以转换到的状态
        valid_from_open = [RoomState.WAITING_FOR_HOST, RoomState.READY, RoomState.CLOSED]
        for target_state in valid_from_open:
            self.assertIn(target_state, self.room_manager.VALID_STATE_TRANSITIONS[RoomState.OPEN])
        
        # 测试从IN_PROGRESS可以转换到的状态
        valid_from_in_progress = [RoomState.ENDED, RoomState.CLOSED]
        for target_state in valid_from_in_progress:
            self.assertIn(target_state, self.room_manager.VALID_STATE_TRANSITIONS[RoomState.IN_PROGRESS])
    
    def test_invalid_transitions(self):
        """测试无效的状态转换"""
        # CLOSED是终态，不能转换到任何状态
        self.assertEqual(self.room_manager.VALID_STATE_TRANSITIONS[RoomState.CLOSED], [])
        
        # SCHEDULED不能直接转换到IN_PROGRESS
        self.assertNotIn(RoomState.IN_PROGRESS, self.room_manager.VALID_STATE_TRANSITIONS[RoomState.SCHEDULED])
        
        # ENDED只能转换到CLOSED
        valid_from_ended = self.room_manager.VALID_STATE_TRANSITIONS[RoomState.ENDED]
        self.assertEqual(valid_from_ended, [RoomState.CLOSED])


class TestRoomLifecycle(RoomStateMachineTestCase):
    """测试房间完整生命周期"""
    
    def test_complete_room_lifecycle(self):
        """测试完整的房间生命周期"""
        # 1. 创建预约房间
        room = Room.objects.create(
            room_code='LIFECYCLE',
            host=self.user1,
            event_template=self.template,
            status=RoomState.SCHEDULED,
            scheduled_start_time=timezone.now() + timedelta(hours=1),
            duration_hours=2
        )
        
        # 2. SCHEDULED -> OPEN
        success, message = self.room_manager.transition_room_state_sync(
            room.room_code, RoomState.OPEN
        )
        self.assertTrue(success, f"Failed to transition to OPEN: {message}")
        
        room.refresh_from_db()
        self.assertEqual(room.status, RoomState.OPEN)
        
        # 3. OPEN -> READY (房主加入)
        success, message = self.room_manager.transition_room_state_sync(
            room.room_code, RoomState.READY
        )
        self.assertTrue(success, f"Failed to transition to READY: {message}")
        
        room.refresh_from_db()
        self.assertEqual(room.status, RoomState.READY)
        
        # 4. READY -> IN_PROGRESS (开始活动)
        success, message = self.room_manager.transition_room_state_sync(
            room.room_code, RoomState.IN_PROGRESS
        )
        self.assertTrue(success, f"Failed to transition to IN_PROGRESS: {message}")
        
        room.refresh_from_db()
        self.assertEqual(room.status, RoomState.IN_PROGRESS)
        
        # 5. IN_PROGRESS -> ENDED (活动结束)
        success, message = self.room_manager.transition_room_state_sync(
            room.room_code, RoomState.ENDED
        )
        self.assertTrue(success, f"Failed to transition to ENDED: {message}")
        
        room.refresh_from_db()
        self.assertEqual(room.status, RoomState.ENDED)
        
        # 6. ENDED -> CLOSED (关闭房间)
        success, message = self.room_manager.transition_room_state_sync(
            room.room_code, RoomState.CLOSED
        )
        self.assertTrue(success, f"Failed to transition to CLOSED: {message}")
        
        room.refresh_from_db()
        self.assertEqual(room.status, RoomState.CLOSED)
    
    def test_alternative_lifecycle_path(self):
        """测试替代的生命周期路径"""
        # 创建房间
        room = Room.objects.create(
            room_code='ALT_PATH',
            host=self.user1,
            event_template=self.template,
            status=RoomState.OPEN,
            duration_hours=2
        )
        
        # OPEN -> WAITING_FOR_HOST (非房主用户加入)
        success, message = self.room_manager.transition_room_state_sync(
            room.room_code, RoomState.WAITING_FOR_HOST
        )
        self.assertTrue(success)
        
        # WAITING_FOR_HOST -> READY (房主确定)
        success, message = self.room_manager.transition_room_state_sync(
            room.room_code, RoomState.READY
        )
        self.assertTrue(success)
        
        # 继续正常流程...
        room.refresh_from_db()
        self.assertEqual(room.status, RoomState.READY)
    
    def test_emergency_closure(self):
        """测试紧急关闭房间"""
        # 创建进行中的房间
        room = Room.objects.create(
            room_code='EMERGENCY',
            host=self.user1,
            event_template=self.template,
            status=RoomState.IN_PROGRESS,
            duration_hours=2
        )
        
        # 从任何状态都可以紧急关闭到CLOSED
        success, message = self.room_manager.transition_room_state_sync(
            room.room_code, RoomState.CLOSED
        )
        self.assertTrue(success)
        
        room.refresh_from_db()
        self.assertEqual(room.status, RoomState.CLOSED)


class TestInvalidTransitions(RoomStateMachineTestCase):
    """测试无效状态转换"""
    
    def test_invalid_transition_from_scheduled(self):
        """测试从SCHEDULED的无效转换"""
        room = Room.objects.create(
            room_code='INVALID1',
            host=self.user1,
            event_template=self.template,
            status=RoomState.SCHEDULED,
            scheduled_start_time=timezone.now() + timedelta(hours=1),
            duration_hours=2
        )
        
        # 尝试从SCHEDULED直接跳转到IN_PROGRESS
        success, message = self.room_manager.transition_room_state_sync(
            room.room_code, RoomState.IN_PROGRESS
        )
        
        self.assertFalse(success)
        self.assertIn('无法从', message)  # 检查中文错误消息
        
        # 验证状态没有改变
        room.refresh_from_db()
        self.assertEqual(room.status, RoomState.SCHEDULED)
    
    def test_ready_to_closed_transition(self):
        """测试房主可以从READY状态关闭房间"""
        room = Room.objects.create(
            room_code='READY_CLOSE',
            host=self.user1,
            event_template=self.template,
            status=RoomState.READY,
            duration_hours=2
        )

        # 房主应该能够关闭房间
        success, message = self.room_manager.transition_room_state_sync(
            room.room_code, RoomState.CLOSED, self.user1
        )
        self.assertTrue(success, f"房主应该能够关闭房间: {message}")

        room.refresh_from_db()
        self.assertEqual(room.status, RoomState.CLOSED)
        self.assertIsNotNone(room.closed_at)

    def test_invalid_transition_from_closed(self):
        """测试从CLOSED的无效转换"""
        room = Room.objects.create(
            room_code='INVALID2',
            host=self.user1,
            event_template=self.template,
            status=RoomState.CLOSED,
            duration_hours=2
        )
        
        # 尝试从CLOSED转换到任何其他状态
        invalid_targets = [
            RoomState.SCHEDULED,
            RoomState.OPEN,
            RoomState.READY,
            RoomState.IN_PROGRESS,
            RoomState.ENDED
        ]
        
        for target_state in invalid_targets:
            success, message = self.room_manager.transition_room_state_sync(
                room.room_code, target_state
            )
            
            self.assertFalse(success, f"Should not be able to transition from CLOSED to {target_state}")
            self.assertIn('无法从', message)  # 检查中文错误消息
    
    def test_nonexistent_room_transition(self):
        """测试不存在房间的状态转换"""
        success, message = self.room_manager.transition_room_state_sync(
            'NONEXIST', RoomState.OPEN
        )
        
        self.assertFalse(success)
        self.assertIn('房间不存在', message)  # 检查中文错误消息


class TestRoomParticipantStates(RoomStateMachineTestCase):
    """测试房间参与者状态"""
    
    def test_participant_state_transitions(self):
        """测试参与者状态转换"""
        room = Room.objects.create(
            room_code='PARTICIPANT',
            host=self.user1,
            event_template=self.template,
            status=RoomState.OPEN,
            duration_hours=2
        )
        
        # 添加参与者
        participant = room.add_participant(self.user2)
        self.assertEqual(participant.state, UserState.JOINED)
        
        # 模拟参与者准备就绪
        participant.state = UserState.READY
        participant.save()
        
        # 房间开始活动时，参与者状态应该变为PLAYING
        room.status = RoomState.IN_PROGRESS
        room.save()
        
        # 这里可以添加更多参与者状态逻辑测试
    
    def test_host_participant_creation(self):
        """测试房主参与者的创建"""
        room = Room.objects.create(
            room_code='HOST_PART',
            host=self.user1,
            event_template=self.template,
            status=RoomState.SCHEDULED,
            scheduled_start_time=timezone.now() + timedelta(hours=1),
            duration_hours=2
        )
        
        # 房主应该自动成为参与者
        host_participant = room.get_host_participant()
        if host_participant:  # 如果实现了自动添加房主逻辑
            self.assertEqual(host_participant.user, self.user1)
            self.assertEqual(host_participant.role, RoomParticipant.ROLE_HOST)


class TestRoomStateTimestamps(RoomStateMachineTestCase):
    """测试房间状态时间戳"""
    
    def test_state_transition_timestamps(self):
        """测试状态转换时间戳记录"""
        room = Room.objects.create(
            room_code='TIMESTAMP',
            host=self.user1,
            event_template=self.template,
            status=RoomState.SCHEDULED,
            scheduled_start_time=timezone.now() + timedelta(hours=1),
            duration_hours=2
        )
        
        # 记录转换前的时间
        before_transition = timezone.now()
        
        # 转换到OPEN状态
        success, message = self.room_manager.transition_room_state_sync(
            room.room_code, RoomState.OPEN
        )
        self.assertTrue(success)
        
        # 记录转换后的时间
        after_transition = timezone.now()
        
        room.refresh_from_db()
        
        # 验证时间戳（如果实现了时间戳记录）
        if hasattr(room, 'opened_at') and room.opened_at:
            self.assertGreaterEqual(room.opened_at, before_transition)
            self.assertLessEqual(room.opened_at, after_transition)


if __name__ == '__main__':
    pytest.main([__file__])
