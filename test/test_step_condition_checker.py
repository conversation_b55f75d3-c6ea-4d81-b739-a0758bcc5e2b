"""
环节开始条件检查器测试

测试环节开始条件检查系统的各种场景，确保房间状态转换的正确性。
"""

import pytest
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone

from core.models import Room, RoomParticipant, RoomState, UserState, RoomEventStep
from core.services.step_condition_checker import (
    PictionaryStepConditionChecker,
    FreeChatStepConditionChecker,
    StepConditionCheckerFactory,
    RoomConditionManager
)
from events.models import EventTemplate

User = get_user_model()


class StepConditionCheckerTestCase(TestCase):
    """环节条件检查器测试基类"""
    
    def setUp(self):
        # 创建测试用户
        self.host = User.objects.create_user(username='host', password='test123')
        self.user1 = User.objects.create_user(username='user1', password='test123')
        self.user2 = User.objects.create_user(username='user2', password='test123')
        
        # 创建测试模板
        self.template = EventTemplate.objects.create(
            name='Test Template',
            creator=self.host
        )
        
        # 创建测试房间
        self.room = Room.objects.create(
            room_code='TEST123',
            host=self.host,
            event_template=self.template,
            status=RoomState.WAITING,
            duration_hours=2
        )
        
        # 添加房主为参与者
        self.host_participant = RoomParticipant.objects.create(
            room=self.room,
            user=self.host,
            role=RoomParticipant.ROLE_HOST,
            state=UserState.JOINED
        )


class PictionaryConditionCheckerTest(StepConditionCheckerTestCase):
    """你画我猜条件检查器测试"""
    
    def setUp(self):
        super().setUp()
        self.checker = PictionaryStepConditionChecker()
    
    def test_pictionary_conditions_with_sufficient_participants(self):
        """测试你画我猜条件 - 参与者足够"""
        # 添加第二个参与者
        RoomParticipant.objects.create(
            room=self.room,
            user=self.user1,
            role=RoomParticipant.ROLE_PARTICIPANT,
            state=UserState.JOINED
        )
        
        is_ready, message, extra_data = self.checker.check_conditions(self.room)
        
        self.assertTrue(is_ready)
        self.assertIn('条件满足', message)
        self.assertEqual(extra_data['participant_count'], 2)
        self.assertIn('host', extra_data['participants'])
        self.assertIn('user1', extra_data['participants'])
    
    def test_pictionary_conditions_insufficient_participants(self):
        """测试你画我猜条件 - 参与者不足"""
        is_ready, message, extra_data = self.checker.check_conditions(self.room)
        
        self.assertFalse(is_ready)
        self.assertIn('需要至少2人参与', message)
        self.assertEqual(extra_data['current_count'], 1)
        self.assertEqual(extra_data['required_count'], 2)
    
    def test_pictionary_conditions_no_host(self):
        """测试你画我猜条件 - 无房主"""
        self.room.host = None
        self.room.save()
        
        is_ready, message, extra_data = self.checker.check_conditions(self.room)
        
        self.assertFalse(is_ready)
        self.assertIn('需要房主在房间中', message)
    
    def test_pictionary_conditions_host_not_participant(self):
        """测试你画我猜条件 - 房主未正确加入房间"""
        # 删除房主参与者记录
        self.host_participant.delete()
        
        is_ready, message, extra_data = self.checker.check_conditions(self.room)
        
        self.assertFalse(is_ready)
        self.assertIn('房主未正确加入房间', message)
    
    def test_pictionary_conditions_wrong_room_status(self):
        """测试你画我猜条件 - 房间状态不正确"""
        # 添加足够的参与者
        RoomParticipant.objects.create(
            room=self.room,
            user=self.user1,
            role=RoomParticipant.ROLE_PARTICIPANT,
            state=UserState.JOINED
        )
        
        # 设置错误的房间状态
        self.room.status = RoomState.IN_PROGRESS
        self.room.save()
        
        is_ready, message, extra_data = self.checker.check_conditions(self.room)
        
        self.assertFalse(is_ready)
        self.assertIn('房间状态不允许开始游戏', message)


class FreeChatConditionCheckerTest(StepConditionCheckerTestCase):
    """自由讨论条件检查器测试"""
    
    def setUp(self):
        super().setUp()
        self.checker = FreeChatStepConditionChecker()
    
    def test_free_chat_conditions_with_host_only(self):
        """测试自由讨论条件 - 仅房主"""
        is_ready, message, extra_data = self.checker.check_conditions(self.room)
        
        self.assertTrue(is_ready)
        self.assertIn('条件满足', message)
        self.assertEqual(extra_data['participant_count'], 1)
        self.assertEqual(extra_data['host'], 'host')
    
    def test_free_chat_conditions_with_multiple_participants(self):
        """测试自由讨论条件 - 多个参与者"""
        # 添加更多参与者
        RoomParticipant.objects.create(
            room=self.room,
            user=self.user1,
            role=RoomParticipant.ROLE_PARTICIPANT,
            state=UserState.JOINED
        )
        RoomParticipant.objects.create(
            room=self.room,
            user=self.user2,
            role=RoomParticipant.ROLE_PARTICIPANT,
            state=UserState.JOINED
        )
        
        is_ready, message, extra_data = self.checker.check_conditions(self.room)
        
        self.assertTrue(is_ready)
        self.assertIn('条件满足', message)
        self.assertEqual(extra_data['participant_count'], 3)
    
    def test_free_chat_conditions_no_host(self):
        """测试自由讨论条件 - 无房主"""
        self.room.host = None
        self.room.save()
        
        is_ready, message, extra_data = self.checker.check_conditions(self.room)
        
        self.assertFalse(is_ready)
        self.assertIn('需要房主在房间中', message)


class StepConditionCheckerFactoryTest(TestCase):
    """环节条件检查器工厂测试"""
    
    def test_create_pictionary_checker(self):
        """测试创建你画我猜检查器"""
        checker = StepConditionCheckerFactory.create_checker('GAME_PICTIONARY')
        self.assertIsInstance(checker, PictionaryStepConditionChecker)
    
    def test_create_free_chat_checker(self):
        """测试创建自由讨论检查器"""
        checker = StepConditionCheckerFactory.create_checker('FREE_CHAT')
        self.assertIsInstance(checker, FreeChatStepConditionChecker)
    
    def test_create_unsupported_checker(self):
        """测试创建不支持的检查器"""
        checker = StepConditionCheckerFactory.create_checker('UNSUPPORTED_TYPE')
        self.assertIsNone(checker)
    
    def test_register_new_checker(self):
        """测试注册新的检查器"""
        class CustomChecker:
            pass
        
        StepConditionCheckerFactory.register_checker('CUSTOM_TYPE', CustomChecker)
        
        checker = StepConditionCheckerFactory.create_checker('CUSTOM_TYPE')
        self.assertIsInstance(checker, CustomChecker)
    
    def test_get_supported_types(self):
        """测试获取支持的类型"""
        types = StepConditionCheckerFactory.get_supported_types()
        self.assertIn('GAME_PICTIONARY', types)
        self.assertIn('FREE_CHAT', types)


class RoomConditionManagerTest(StepConditionCheckerTestCase):
    """房间条件管理器测试"""
    
    def setUp(self):
        super().setUp()
        self.manager = RoomConditionManager()
        
        # 创建房间环节
        self.step = RoomEventStep.objects.create(
            room=self.room,
            order=1,
            name='你画我猜',
            step_type='GAME_PICTIONARY',
            duration=300
        )
    
    def test_check_next_step_conditions_ready(self):
        """测试检查下一环节条件 - 准备就绪"""
        # 添加足够的参与者
        RoomParticipant.objects.create(
            room=self.room,
            user=self.user1,
            role=RoomParticipant.ROLE_PARTICIPANT,
            state=UserState.JOINED
        )
        
        is_ready, message, extra_data = self.manager.check_next_step_conditions(self.room)
        
        self.assertTrue(is_ready)
        self.assertIn('条件满足', message)
        self.assertIsNotNone(extra_data.get('next_step'))
        self.assertEqual(extra_data['next_step']['step_type'], 'GAME_PICTIONARY')
    
    def test_check_next_step_conditions_not_ready(self):
        """测试检查下一环节条件 - 未准备就绪"""
        is_ready, message, extra_data = self.manager.check_next_step_conditions(self.room)
        
        self.assertFalse(is_ready)
        self.assertIn('需要至少2人参与', message)
    
    def test_check_next_step_conditions_no_more_steps(self):
        """测试检查下一环节条件 - 没有更多环节"""
        # 删除所有环节
        RoomEventStep.objects.filter(room=self.room).delete()
        
        is_ready, message, extra_data = self.manager.check_next_step_conditions(self.room)
        
        self.assertFalse(is_ready)
        self.assertIn('没有更多环节', message)
        self.assertEqual(extra_data.get('reason'), 'no_more_steps')
    
    def test_check_next_step_conditions_unsupported_type(self):
        """测试检查下一环节条件 - 不支持的环节类型"""
        # 修改环节类型为不支持的类型
        self.step.step_type = 'UNSUPPORTED_TYPE'
        self.step.save()
        
        is_ready, message, extra_data = self.manager.check_next_step_conditions(self.room)
        
        # 应该使用默认条件（只需要房主）
        self.assertTrue(is_ready)
        self.assertIn('条件满足', message)
